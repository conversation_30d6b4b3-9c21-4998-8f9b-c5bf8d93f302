from datetime import datetime

def create_base_email_template(title, content, button_text=None, button_url=None, accent_color="#4A90E2"):
    """
    Base email template with sky blue theme
    """
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap');
        </style>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Montserrat', Arial, sans-serif; background-color: #F5F8FB; color: #2C3E50;">
        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
                <td style="padding: 20px 0;">
                    <table role="presentation" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse; margin: 0 auto; background: #FFFFFF; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        
                        <!-- Header with Logo -->
                        <tr>
                            <td style="padding: 30px 40px 20px; text-align: center; background: linear-gradient(135deg, {accent_color} 0%, #2C5282 100%); border-radius: 15px 15px 0 0;">
                                <div style="display: inline-block; background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 15px; backdrop-filter: blur(10px);">
                                    <h1 style="color: #FFFFFF; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FUNDED<span style="color: #BEE3F8;">WHALES</span></h1>
                                    <p style="color: #E2E8F0; margin: 5px 0 0; font-size: 14px; letter-spacing: 3px; text-transform: uppercase;">PROP TRADING</p>
                                </div>
                            </td>
                        </tr>

                        <!-- Title Banner -->
                        <tr>
                            <td style="padding: 0;">
                                <div style="background: linear-gradient(135deg, #EBF4FF 0%, #F0F7FF 100%); padding: 30px; text-align: center; border-bottom: 1px solid #E2E8F0;">
                                    <h2 style="color: {accent_color}; margin: 0; font-size: 24px; letter-spacing: 1px; font-weight: 700;">{title}</h2>
                                </div>
                            </td>
                        </tr>

                        <!-- Content -->
                        <tr>
                            <td style="padding: 30px 40px;">
                                <div style="color: #4A5568; font-size: 16px; line-height: 1.6;">
                                    {content}
                                </div>
                                {f'''
                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="margin-top: 30px;">
                                    <tr>
                                        <td style="text-align: center;">
                                            <a href="{button_url}" style="display: inline-block; background: linear-gradient(135deg, {accent_color} 0%, #2C5282 100%); color: #FFFFFF; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; font-size: 16px; text-transform: uppercase; letter-spacing: 1px; transition: all 0.3s ease; box-shadow: 0 4px 6px rgba(74, 144, 226, 0.2);">{button_text}</a>
                                        </td>
                                    </tr>
                                </table>
                                ''' if button_text and button_url else ''}
                            </td>
                        </tr>

                        <!-- Footer -->
                        <tr>
                            <td style="background: #F7FAFC; padding: 20px; text-align: center; border-radius: 0 0 15px 15px; border-top: 1px solid #E2E8F0;">
                                <p style="color: #718096; margin: 0; font-size: 14px;">© {datetime.now().year} FundedWhales Prop Trading. All rights reserved.</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
    return html_template

def create_welcome_email(username):
    """
    Welcome email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">Welcome to <span style="color: #4A90E2; font-weight: 600;">FundedWhales</span>! We're thrilled to have you join our exclusive trading community.</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Your Next Steps:</h4>
        <ol style="margin: 0; padding-left: 20px; color: #4A5568;">
            <li style="margin-bottom: 10px;"><span style="color: #2C5282; font-weight: 600;">Complete Your Profile</span> - Set up your trading preferences</li>
            <li style="margin-bottom: 10px;"><span style="color: #2C5282; font-weight: 600;">Explore Challenges</span> - Browse our trading challenge options</li>
            <li><span style="color: #2C5282; font-weight: 600;">Start Trading</span> - Begin your journey to becoming a funded trader</li>
        </ol>
    </div>
    
    <p style="margin: 20px 0;">Ready to start your professional trading career?</p>
    """
    return create_base_email_template(
        "Welcome to FundedWhales",
        content,
        "Access Dashboard",
        "https://fundedwhales.com/dashboard"
    )

def create_order_confirmation_email(username, order_id, challenge_type, account_size, platform):
    """
    Order confirmation email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">Thank you for your order! We're excited to confirm that your trading challenge has been successfully processed.</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Order Details:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Order Number:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">FxE{order_id}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Challenge Type:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{challenge_type}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Account Size:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{account_size}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Platform:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{platform}</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Your trading challenge will be prepared shortly. We'll send you another email with your login credentials once everything is ready.</p>
    """
    return create_base_email_template(
        "Order Confirmation",
        content,
        "View Order Details",
        f"https://fundedwhales.com/orders/{order_id}"
    )

def create_challenge_completion_email(username, order_id, server, login, password):
    """
    Challenge completion email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">Your trading challenge account has been successfully set up and is ready for trading!</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Your Trading Account Credentials:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Server:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{server}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Login:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{login}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Password:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{password}</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Please make sure to read our trading rules and risk management guidelines before starting your challenge.</p>
    """
    return create_base_email_template(
        "Your Challenge Account is Ready",
        content,
        "Start Trading Now",
        f"https://fundedwhales.com/dashboard/challenge/{order_id}"
    )

def create_pass_notification_email(username, order_id, profit_amount=None):
    """
    Challenge pass notification email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <div style="text-align: center; margin: 30px 0;">
        <div style="display: inline-block; background: #F0FFF4; border: 2px solid #48BB78; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 20px;">
            <span style="color: #48BB78; font-size: 40px;">✓</span>
        </div>
        <h2 style="color: #48BB78; margin: 0;">Congratulations!</h2>
    </div>
    
    <p style="margin: 0 0 20px; text-align: center; font-size: 18px;">You've successfully passed your trading challenge!</p>
    
    {f'<p style="margin: 20px 0; text-align: center; color: #2C5282; font-weight: 600;">Total Profit: ${profit_amount}</p>' if profit_amount else ''}
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Next Steps:</h4>
        <ol style="margin: 0; padding-left: 20px; color: #4A5568;">
            <li style="margin-bottom: 10px;">Our team will review your trading performance</li>
            <li style="margin-bottom: 10px;">We'll prepare your funded account credentials</li>
            <li>You'll receive another email with your funded account details</li>
        </ol>
    </div>
    """
    return create_base_email_template(
        "Challenge Passed Successfully!",
        content,
        "View Performance",
        f"https://fundedwhales.com/dashboard/challenge/{order_id}/results"
    )

def create_fail_notification_email(username, order_id, reason):
    """
    Challenge fail notification email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">We regret to inform you that your trading challenge has been marked as failed.</p>
    
    <div style="background: #FFF5F5; border: 1px solid #FC8181; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #C53030; margin: 0 0 15px;">Reason for Failure:</h4>
        <p style="margin: 0; color: #4A5568;">{reason}</p>
    </div>
    
    <p style="margin: 20px 0;">Don't give up! Many successful traders faced setbacks before achieving their goals. You can always try again with a new challenge.</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">What's Next?</h4>
        <ul style="margin: 0; padding-left: 20px; color: #4A5568;">
            <li style="margin-bottom: 10px;">Review your trading performance and learn from this experience</li>
            <li style="margin-bottom: 10px;">Consider our educational resources to improve your strategy</li>
            <li>Start a new challenge when you're ready</li>
        </ul>
    </div>
    """
    return create_base_email_template(
        "Challenge Status Update",
        content,
        "Try Again",
        "https://fundedwhales.com/challenges"
    )

def create_live_account_email(username, order_id, server, login, password, profit_share):
    """
    Live account creation email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <div style="text-align: center; margin: 30px 0;">
        <div style="display: inline-block; background: #F0FFF4; border: 2px solid #48BB78; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 20px;">
            <span style="color: #48BB78; font-size: 40px;">🌟</span>
        </div>
        <h2 style="color: #48BB78; margin: 0;">Welcome to the Funded Traders Club!</h2>
    </div>
    
    <p style="margin: 0 0 20px;">Your live trading account has been successfully created. Here are your account details:</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Live Account Credentials:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Server:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{server}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Login:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{login}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Password:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{password}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Profit Share:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{profit_share}%</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Remember to follow our trading rules and risk management guidelines. Happy trading!</p>
    """
    return create_base_email_template(
        "Your Live Account is Ready",
        content,
        "Start Live Trading",
        f"https://fundedwhales.com/dashboard/live/{order_id}"
    )

def create_certificate_email(username, order_id, challenge_type, account_size, certificate_id, issue_date, profit_amount=None):
    """
    Advanced certificate email template with luxury design
    """
    content = f"""
    <div style="background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%); border-radius: 15px; padding: 3px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%); border-radius: 12px; padding: 40px; position: relative; overflow: hidden;">
            <!-- Decorative Elements -->
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNMCAwbDUwIDUwTDAgMTAwIiBzdHJva2U9InJnYmEoNzQsIDE0NCwgMjI2LCAwLjEpIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4=') repeat; opacity: 0.1;"></div>
            
            <!-- Certificate Header -->
            <div style="text-align: center; position: relative; z-index: 1;">
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #4A90E2; margin: 0; font-size: 36px; letter-spacing: 3px; text-transform: uppercase; font-weight: 800; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">Certificate of Achievement</h1>
                    <div style="width: 100px; height: 3px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 15px auto;"></div>
                    <p style="color: #2d3748; font-size: 18px; margin: 10px 0;">FundedWhales Trading Excellence</p>
                </div>
                
                <!-- Recipient Info -->
                <div style="margin: 40px 0;">
                    <p style="color: #4a5568; font-size: 16px; margin: 0;">This certificate is proudly presented to</p>
                    <h2 style="color: #2d3748; font-size: 32px; margin: 15px 0; font-family: 'Playfair Display', serif;">{username}</h2>
                    <p style="color: #4a5568; font-size: 16px; line-height: 1.6; max-width: 600px; margin: 20px auto;">
                        For demonstrating exceptional trading proficiency and successfully completing the FundedWhales Trading Challenge
                        with outstanding performance and adherence to risk management principles.
                    </p>
                </div>
                
                <!-- Achievement Details -->
                <div style="background: linear-gradient(135deg, #EBF8FF 0%, #F0F7FF 100%); border: 1px solid rgba(74, 144, 226, 0.2); border-radius: 12px; padding: 25px; margin: 30px auto; max-width: 500px; box-shadow: 0 4px 6px rgba(74, 144, 226, 0.1);">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Challenge Type:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #2d3748; font-weight: 600;">{challenge_type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Account Size:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #2d3748; font-weight: 600;">{account_size}</td>
                        </tr>
                        {f'''
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Total Profit:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #48BB78; font-weight: 600;">${profit_amount}</td>
                        </tr>
                        ''' if profit_amount else ''}
                        <tr>
                            <td style="padding: 12px; color: #4a5568;">Certificate ID:</td>
                            <td style="padding: 12px; color: #2d3748; font-weight: 600;">{certificate_id}</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Signatures -->
                <div style="display: flex; justify-content: space-between; margin-top: 40px; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 200px; text-align: center; margin: 20px;">
                        <div style="font-family: 'Dancing Script', cursive; color: #2d3748; font-size: 28px;">Maxwell Grant</div>
                        <div style="width: 80%; height: 2px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 10px auto;"></div>
                        <p style="color: #4a5568; margin: 5px 0 0; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Chief Executive Officer</p>
                    </div>
                    <div style="flex: 1; min-width: 200px; text-align: center; margin: 20px;">
                        <div style="font-family: 'Montserrat', sans-serif; color: #2d3748; font-size: 18px;">{issue_date}</div>
                        <div style="width: 80%; height: 2px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 10px auto;"></div>
                        <p style="color: #4a5568; margin: 5px 0 0; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Date of Issue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    """
    return create_base_email_template(
        "Trading Excellence Certificate",
        content,
        "View Certificate",
        f"https://fundedwhales.com/certificates/{certificate_id}"
    ) 